import Foundation
import Combine

// MARK: - TMDB API Service
class TMDBService: ObservableObject {
    static let shared = TMDBService()
    
    private let apiKey = "YOUR_TMDB_API_KEY" // Replace with actual API key
    private let baseURL = "https://api.themoviedb.org/3"
    private let imageBaseURL = "https://image.tmdb.org/t/p"
    
    private let session = URLSession.shared
    private var cancellables = Set<AnyCancellable>()
    
    private init() {}
    
    // MARK: - Image URL Helpers
    func posterURL(for path: String?, size: ImageSize = .w500) -> URL? {
        guard let path = path else { return nil }
        return URL(string: "\(imageBaseURL)/\(size.rawValue)\(path)")
    }
    
    func backdropURL(for path: String?, size: ImageSize = .w1280) -> URL? {
        guard let path = path else { return nil }
        return URL(string: "\(imageBaseURL)/\(size.rawValue)\(path)")
    }
    
    // MARK: - Movies
    func fetchTrendingMovies() async throws -> [TMDBMovie] {
        let url = URL(string: "\(baseURL)/trending/movie/week?api_key=\(apiKey)")!
        let (data, _) = try await session.data(from: url)
        let response = try JSONDecoder().decode(TMDBMovieResponse.self, from: data)
        return response.results
    }
    
    func fetchPopularMovies() async throws -> [TMDBMovie] {
        let url = URL(string: "\(baseURL)/movie/popular?api_key=\(apiKey)")!
        let (data, _) = try await session.data(from: url)
        let response = try JSONDecoder().decode(TMDBMovieResponse.self, from: data)
        return response.results
    }
    
    func fetchMovieDetails(id: Int) async throws -> TMDBMovieDetails {
        let url = URL(string: "\(baseURL)/movie/\(id)?api_key=\(apiKey)&append_to_response=credits,videos,reviews")!
        let (data, _) = try await session.data(from: url)
        return try JSONDecoder().decode(TMDBMovieDetails.self, from: data)
    }
    
    // MARK: - TV Shows
    func fetchTrendingTVShows() async throws -> [TMDBTVShow] {
        let url = URL(string: "\(baseURL)/trending/tv/week?api_key=\(apiKey)")!
        let (data, _) = try await session.data(from: url)
        let response = try JSONDecoder().decode(TMDBTVResponse.self, from: data)
        return response.results
    }
    
    func fetchPopularTVShows() async throws -> [TMDBTVShow] {
        let url = URL(string: "\(baseURL)/tv/popular?api_key=\(apiKey)")!
        let (data, _) = try await session.data(from: url)
        let response = try JSONDecoder().decode(TMDBTVResponse.self, from: data)
        return response.results
    }
    
    func fetchTVShowDetails(id: Int) async throws -> TMDBTVShowDetails {
        let url = URL(string: "\(baseURL)/tv/\(id)?api_key=\(apiKey)&append_to_response=credits,videos,reviews")!
        let (data, _) = try await session.data(from: url)
        return try JSONDecoder().decode(TMDBTVShowDetails.self, from: data)
    }
    
    func fetchTVShowSeason(showId: Int, seasonNumber: Int) async throws -> TMDBSeason {
        let url = URL(string: "\(baseURL)/tv/\(showId)/season/\(seasonNumber)?api_key=\(apiKey)")!
        let (data, _) = try await session.data(from: url)
        return try JSONDecoder().decode(TMDBSeason.self, from: data)
    }
    
    // MARK: - Search
    func searchMulti(query: String) async throws -> [TMDBSearchResult] {
        guard let encodedQuery = query.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) else {
            throw TMDBError.invalidQuery
        }
        
        let url = URL(string: "\(baseURL)/search/multi?api_key=\(apiKey)&query=\(encodedQuery)")!
        let (data, _) = try await session.data(from: url)
        let response = try JSONDecoder().decode(TMDBSearchResponse.self, from: data)
        return response.results
    }
}

// MARK: - Data Models
struct TMDBMovieResponse: Codable {
    let results: [TMDBMovie]
}

struct TMDBTVResponse: Codable {
    let results: [TMDBTVShow]
}

struct TMDBSearchResponse: Codable {
    let results: [TMDBSearchResult]
}

struct TMDBMovie: Codable, Identifiable {
    let id: Int
    let title: String
    let overview: String?
    let releaseDate: String?
    let posterPath: String?
    let backdropPath: String?
    let voteAverage: Double
    let voteCount: Int
    let popularity: Double
    let genreIds: [Int]
    
    enum CodingKeys: String, CodingKey {
        case id, title, overview, popularity
        case releaseDate = "release_date"
        case posterPath = "poster_path"
        case backdropPath = "backdrop_path"
        case voteAverage = "vote_average"
        case voteCount = "vote_count"
        case genreIds = "genre_ids"
    }
}

struct TMDBTVShow: Codable, Identifiable {
    let id: Int
    let name: String
    let overview: String?
    let firstAirDate: String?
    let posterPath: String?
    let backdropPath: String?
    let voteAverage: Double
    let voteCount: Int
    let popularity: Double
    let genreIds: [Int]
    
    enum CodingKeys: String, CodingKey {
        case id, name, overview, popularity
        case firstAirDate = "first_air_date"
        case posterPath = "poster_path"
        case backdropPath = "backdrop_path"
        case voteAverage = "vote_average"
        case voteCount = "vote_count"
        case genreIds = "genre_ids"
    }
}

struct TMDBMovieDetails: Codable {
    let id: Int
    let title: String
    let overview: String?
    let releaseDate: String?
    let posterPath: String?
    let backdropPath: String?
    let voteAverage: Double
    let runtime: Int?
    let genres: [TMDBGenre]
    let credits: TMDBCredits?
    let videos: TMDBVideos?
    let reviews: TMDBReviews?
    
    enum CodingKeys: String, CodingKey {
        case id, title, overview, runtime, genres, credits, videos, reviews
        case releaseDate = "release_date"
        case posterPath = "poster_path"
        case backdropPath = "backdrop_path"
        case voteAverage = "vote_average"
    }
}

struct TMDBTVShowDetails: Codable {
    let id: Int
    let name: String
    let overview: String?
    let firstAirDate: String?
    let lastAirDate: String?
    let posterPath: String?
    let backdropPath: String?
    let voteAverage: Double
    let numberOfSeasons: Int
    let numberOfEpisodes: Int
    let genres: [TMDBGenre]
    let credits: TMDBCredits?
    let videos: TMDBVideos?
    let reviews: TMDBReviews?
    let status: String
    
    enum CodingKeys: String, CodingKey {
        case id, name, overview, genres, credits, videos, reviews, status
        case firstAirDate = "first_air_date"
        case lastAirDate = "last_air_date"
        case posterPath = "poster_path"
        case backdropPath = "backdrop_path"
        case voteAverage = "vote_average"
        case numberOfSeasons = "number_of_seasons"
        case numberOfEpisodes = "number_of_episodes"
    }
}

struct TMDBSeason: Codable {
    let id: Int
    let name: String
    let overview: String?
    let posterPath: String?
    let seasonNumber: Int
    let episodes: [TMDBEpisode]
    
    enum CodingKeys: String, CodingKey {
        case id, name, overview, episodes
        case posterPath = "poster_path"
        case seasonNumber = "season_number"
    }
}

struct TMDBEpisode: Codable {
    let id: Int
    let name: String
    let overview: String?
    let airDate: String?
    let episodeNumber: Int
    let seasonNumber: Int
    let stillPath: String?
    let voteAverage: Double
    let runtime: Int?
    
    enum CodingKeys: String, CodingKey {
        case id, name, overview, runtime
        case airDate = "air_date"
        case episodeNumber = "episode_number"
        case seasonNumber = "season_number"
        case stillPath = "still_path"
        case voteAverage = "vote_average"
    }
}

struct TMDBGenre: Codable {
    let id: Int
    let name: String
}

struct TMDBCredits: Codable {
    let cast: [TMDBCastMember]
    let crew: [TMDBCrewMember]
}

struct TMDBCastMember: Codable {
    let id: Int
    let name: String
    let character: String?
    let profilePath: String?
    
    enum CodingKeys: String, CodingKey {
        case id, name, character
        case profilePath = "profile_path"
    }
}

struct TMDBCrewMember: Codable {
    let id: Int
    let name: String
    let job: String
    let department: String
    let profilePath: String?
    
    enum CodingKeys: String, CodingKey {
        case id, name, job, department
        case profilePath = "profile_path"
    }
}

struct TMDBVideos: Codable {
    let results: [TMDBVideo]
}

struct TMDBVideo: Codable {
    let id: String
    let key: String
    let name: String
    let site: String
    let type: String
}

struct TMDBReviews: Codable {
    let results: [TMDBReview]
}

struct TMDBReview: Codable {
    let id: String
    let author: String
    let content: String
    let createdAt: String
    
    enum CodingKeys: String, CodingKey {
        case id, author, content
        case createdAt = "created_at"
    }
}

struct TMDBSearchResult: Codable, Identifiable {
    let id: Int
    let mediaType: String
    let title: String?
    let name: String?
    let overview: String?
    let posterPath: String?
    let backdropPath: String?
    let releaseDate: String?
    let firstAirDate: String?
    let voteAverage: Double
    
    enum CodingKeys: String, CodingKey {
        case id, overview
        case mediaType = "media_type"
        case title, name
        case posterPath = "poster_path"
        case backdropPath = "backdrop_path"
        case releaseDate = "release_date"
        case firstAirDate = "first_air_date"
        case voteAverage = "vote_average"
    }
    
    var displayTitle: String {
        return title ?? name ?? "Unknown"
    }
}

// MARK: - Supporting Types
enum ImageSize: String {
    case w92, w154, w185, w342, w500, w780
    case w1280 = "w1280"
    case original
}

enum TMDBError: Error {
    case invalidQuery
    case noData
    case decodingError
}
