import Foundation
import CoreML
import CoreData
import Combine

// MARK: - Recommendation Engine
class RecommendationEngine: ObservableObject {
    static let shared = RecommendationEngine()
    
    private let tmdbService = TMDBService.shared
    private var cancellables = Set<AnyCancellable>()
    
    @Published var movieRecommendations: [TMDBMovie] = []
    @Published var tvShowRecommendations: [TMDBTVShow] = []
    @Published var isLoading = false
    
    private init() {}
    
    // MARK: - Main Recommendation Methods
    func generateRecommendations(for context: NSManagedObjectContext) async {
        await MainActor.run {
            isLoading = true
        }
        
        do {
            // Get user's watch history and ratings
            let userProfile = await buildUserProfile(context: context)
            
            // Generate movie recommendations
            let movieRecs = try await generateMovieRecommendations(userProfile: userProfile)
            
            // Generate TV show recommendations
            let tvRecs = try await generateTVShowRecommendations(userProfile: userProfile)
            
            await MainActor.run {
                self.movieRecommendations = movieRecs
                self.tvShowRecommendations = tvRecs
                self.isLoading = false
            }
        } catch {
            print("Error generating recommendations: \(error)")
            await MainActor.run {
                self.isLoading = false
            }
        }
    }
    
    // MARK: - User Profile Building
    private func buildUserProfile(context: NSManagedObjectContext) async -> UserProfile {
        return await withCheckedContinuation { continuation in
            context.perform {
                let movieRequest: NSFetchRequest<Movie> = Movie.fetchRequest()
                movieRequest.predicate = NSPredicate(format: "isWatched == YES")
                
                let tvShowRequest: NSFetchRequest<TVShow> = TVShow.fetchRequest()
                tvShowRequest.predicate = NSPredicate(format: "isWatched == YES")
                
                do {
                    let watchedMovies = try context.fetch(movieRequest)
                    let watchedTVShows = try context.fetch(tvShowRequest)
                    
                    let profile = UserProfile(
                        watchedMovies: watchedMovies,
                        watchedTVShows: watchedTVShows
                    )
                    
                    continuation.resume(returning: profile)
                } catch {
                    print("Error fetching user data: \(error)")
                    continuation.resume(returning: UserProfile(watchedMovies: [], watchedTVShows: []))
                }
            }
        }
    }
    
    // MARK: - Movie Recommendations
    private func generateMovieRecommendations(userProfile: UserProfile) async throws -> [TMDBMovie] {
        // If user has no watch history, return trending movies
        if userProfile.watchedMovies.isEmpty {
            return try await tmdbService.fetchTrendingMovies()
        }
        
        // Analyze user preferences
        let preferences = analyzeMoviePreferences(userProfile.watchedMovies)
        
        // Get recommendations based on preferences
        var recommendations: [TMDBMovie] = []
        
        // Fetch popular movies and filter based on preferences
        let popularMovies = try await tmdbService.fetchPopularMovies()
        
        // Score movies based on user preferences
        let scoredMovies = popularMovies.map { movie in
            (movie: movie, score: calculateMovieScore(movie, preferences: preferences))
        }
        
        // Sort by score and take top recommendations
        recommendations = scoredMovies
            .sorted { $0.score > $1.score }
            .prefix(20)
            .map { $0.movie }
        
        // Mix in some trending content for diversity
        let trendingMovies = try await tmdbService.fetchTrendingMovies()
        recommendations.append(contentsOf: trendingMovies.prefix(5))
        
        return Array(recommendations.prefix(20))
    }
    
    // MARK: - TV Show Recommendations
    private func generateTVShowRecommendations(userProfile: UserProfile) async throws -> [TMDBTVShow] {
        // If user has no watch history, return trending TV shows
        if userProfile.watchedTVShows.isEmpty {
            return try await tmdbService.fetchTrendingTVShows()
        }
        
        // Analyze user preferences
        let preferences = analyzeTVShowPreferences(userProfile.watchedTVShows)
        
        // Get recommendations based on preferences
        var recommendations: [TMDBTVShow] = []
        
        // Fetch popular TV shows and filter based on preferences
        let popularTVShows = try await tmdbService.fetchPopularTVShows()
        
        // Score TV shows based on user preferences
        let scoredTVShows = popularTVShows.map { tvShow in
            (tvShow: tvShow, score: calculateTVShowScore(tvShow, preferences: preferences))
        }
        
        // Sort by score and take top recommendations
        recommendations = scoredTVShows
            .sorted { $0.score > $1.score }
            .prefix(20)
            .map { $0.tvShow }
        
        // Mix in some trending content for diversity
        let trendingTVShows = try await tmdbService.fetchTrendingTVShows()
        recommendations.append(contentsOf: trendingTVShows.prefix(5))
        
        return Array(recommendations.prefix(20))
    }
    
    // MARK: - Preference Analysis
    private func analyzeMoviePreferences(_ movies: [Movie]) -> MoviePreferences {
        var genreScores: [String: Double] = [:]
        var ratingSum: Double = 0
        var ratingCount: Int = 0
        
        for movie in movies {
            // Analyze genres
            if let genres = movie.genres {
                let genreList = genres.components(separatedBy: ",")
                for genre in genreList {
                    let trimmedGenre = genre.trimmingCharacters(in: .whitespaces)
                    genreScores[trimmedGenre, default: 0] += movie.userRating
                }
            }
            
            // Calculate average rating
            if movie.userRating > 0 {
                ratingSum += movie.userRating
                ratingCount += 1
            }
        }
        
        let averageRating = ratingCount > 0 ? ratingSum / Double(ratingCount) : 7.0
        
        return MoviePreferences(
            preferredGenres: genreScores,
            averageRating: averageRating,
            totalWatched: movies.count
        )
    }
    
    private func analyzeTVShowPreferences(_ tvShows: [TVShow]) -> TVShowPreferences {
        var genreScores: [String: Double] = [:]
        var ratingSum: Double = 0
        var ratingCount: Int = 0
        
        for tvShow in tvShows {
            // Analyze genres
            if let genres = tvShow.genres {
                let genreList = genres.components(separatedBy: ",")
                for genre in genreList {
                    let trimmedGenre = genre.trimmingCharacters(in: .whitespaces)
                    genreScores[trimmedGenre, default: 0] += tvShow.userRating
                }
            }
            
            // Calculate average rating
            if tvShow.userRating > 0 {
                ratingSum += tvShow.userRating
                ratingCount += 1
            }
        }
        
        let averageRating = ratingCount > 0 ? ratingSum / Double(ratingCount) : 7.0
        
        return TVShowPreferences(
            preferredGenres: genreScores,
            averageRating: averageRating,
            totalWatched: tvShows.count
        )
    }
    
    // MARK: - Scoring Algorithms
    private func calculateMovieScore(_ movie: TMDBMovie, preferences: MoviePreferences) -> Double {
        var score: Double = 0
        
        // Base score from TMDB rating
        score += movie.voteAverage * 0.3
        
        // Popularity boost
        score += min(movie.popularity / 1000, 2.0) * 0.2
        
        // Genre preference matching
        let genreBonus = calculateGenreBonus(genreIds: movie.genreIds, preferences: preferences.preferredGenres)
        score += genreBonus * 0.5
        
        return score
    }
    
    private func calculateTVShowScore(_ tvShow: TMDBTVShow, preferences: TVShowPreferences) -> Double {
        var score: Double = 0
        
        // Base score from TMDB rating
        score += tvShow.voteAverage * 0.3
        
        // Popularity boost
        score += min(tvShow.popularity / 1000, 2.0) * 0.2
        
        // Genre preference matching
        let genreBonus = calculateGenreBonus(genreIds: tvShow.genreIds, preferences: preferences.preferredGenres)
        score += genreBonus * 0.5
        
        return score
    }
    
    private func calculateGenreBonus(genreIds: [Int], preferences: [String: Double]) -> Double {
        // This is a simplified genre matching - in a real app, you'd have a genre ID to name mapping
        // For now, we'll use a basic scoring system
        let genreMapping: [Int: String] = [
            28: "Action", 12: "Adventure", 16: "Animation", 35: "Comedy",
            80: "Crime", 99: "Documentary", 18: "Drama", 10751: "Family",
            14: "Fantasy", 36: "History", 27: "Horror", 10402: "Music",
            9648: "Mystery", 10749: "Romance", 878: "Science Fiction",
            10770: "TV Movie", 53: "Thriller", 10752: "War", 37: "Western"
        ]
        
        var bonus: Double = 0
        for genreId in genreIds {
            if let genreName = genreMapping[genreId],
               let preference = preferences[genreName] {
                bonus += preference / 10.0 // Normalize the bonus
            }
        }
        
        return bonus
    }
}

// MARK: - Supporting Data Structures
struct UserProfile {
    let watchedMovies: [Movie]
    let watchedTVShows: [TVShow]
}

struct MoviePreferences {
    let preferredGenres: [String: Double]
    let averageRating: Double
    let totalWatched: Int
}

struct TVShowPreferences {
    let preferredGenres: [String: Double]
    let averageRating: Double
    let totalWatched: Int
}
