import SwiftUI

struct ContentView: View {
    @State private var selectedTab = 0
    
    var body: some View {
        TabView(selection: $selectedTab) {
            HomeView()
                .tabItem {
                    Image(systemName: "house.fill")
                    Text("Home")
                }
                .tag(0)
            
            MoviesView()
                .tabItem {
                    Image(systemName: "film.fill")
                    Text("Movies")
                }
                .tag(1)
            
            TVShowsView()
                .tabItem {
                    Image(systemName: "tv.fill")
                    Text("TV Shows")
                }
                .tag(2)
            
            SearchView()
                .tabItem {
                    Image(systemName: "magnifyingglass")
                    Text("Search")
                }
                .tag(3)
            
            ProfileView()
                .tabItem {
                    Image(systemName: "person.fill")
                    Text("Profile")
                }
                .tag(4)
        }
        .accentColor(.blue)
}

// MARK: - Home View
struct HomeView: View {
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // Welcome Header
                    VStack(alignment: .leading, spacing: 8) {
                        HStack {
                            VStack(alignment: .leading) {
                                Text("Welcome back!")
                                    .font(.title2)
                                    .fontWeight(.medium)
                                
                                Text("Continue watching or discover something new")
                                    .font(.subheadline)
                                    .foregroundColor(.secondary)
                            }
                            
                            Spacer()
                            
                            Button(action: {}) {
                                Image(systemName: "bell")
                                    .font(.title2)
                                    .foregroundColor(.primary)
                            }
                        }
                        .padding(.horizontal)
                    }
                    
                    // Continue Watching Section
                    MediaCarouselSection(
                        title: "Continue Watching",
                        subtitle: "Pick up where you left off"
                    ) {
                        // Placeholder for continue watching items
                        ForEach(0..<5, id: \.self) { _ in
                            ContinueWatchingCard()
                        }
                    }
                    
                    // AI Recommendations Section
                    MediaCarouselSection(
                        title: "Recommended for You",
                        subtitle: "Based on your viewing history"
                    ) {
                        // Placeholder for AI recommendations
                        ForEach(0..<10, id: \.self) { _ in
                            MediaCard()
                        }
                    }
                    
                    // Trending Section
                    MediaCarouselSection(
                        title: "Trending Now",
                        subtitle: "What everyone's watching"
                    ) {
                        ForEach(0..<10, id: \.self) { _ in
                            MediaCard()
                        }
                    }
                    
                    // Recently Added Section
                    MediaCarouselSection(
                        title: "Recently Added",
                        subtitle: "New to your watchlist"
                    ) {
                        ForEach(0..<10, id: \.self) { _ in
                            MediaCard()
                        }
                    }
                }
                .padding(.top)
            }
            .navigationTitle("")
        }
    }
}

// MARK: - Movies View
struct MoviesView: View {
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    Text("Movies")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                        .padding()

                    // Popular Movies Section
                    VStack(alignment: .leading, spacing: 12) {
                        HStack {
                            Text("Popular Movies")
                                .font(.title2)
                                .fontWeight(.semibold)
                            Spacer()
                        }
                        .padding(.horizontal)

                        ScrollView(.horizontal, showsIndicators: false) {
                            HStack(spacing: 16) {
                                ForEach(0..<10, id: \.self) { _ in
                                    MediaCard()
                                }
                            }
                            .padding(.horizontal)
                        }
                    }

                    // Top Rated Movies Section
                    VStack(alignment: .leading, spacing: 12) {
                        HStack {
                            Text("Top Rated")
                                .font(.title2)
                                .fontWeight(.semibold)
                            Spacer()
                        }
                        .padding(.horizontal)

                        ScrollView(.horizontal, showsIndicators: false) {
                            HStack(spacing: 16) {
                                ForEach(0..<10, id: \.self) { _ in
                                    MediaCard()
                                }
                            }
                            .padding(.horizontal)
                        }
                    }
                }
            }
            .navigationTitle("Movies")
        }
    }
}

// MARK: - TV Shows View
struct TVShowsView: View {
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    Text("TV Shows")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                        .padding()

                    // Popular TV Shows Section
                    VStack(alignment: .leading, spacing: 12) {
                        HStack {
                            Text("Popular Shows")
                                .font(.title2)
                                .fontWeight(.semibold)
                            Spacer()
                        }
                        .padding(.horizontal)

                        ScrollView(.horizontal, showsIndicators: false) {
                            HStack(spacing: 16) {
                                ForEach(0..<10, id: \.self) { _ in
                                    MediaCard()
                                }
                            }
                            .padding(.horizontal)
                        }
                    }

                    // Trending Shows Section
                    VStack(alignment: .leading, spacing: 12) {
                        HStack {
                            Text("Trending Shows")
                                .font(.title2)
                                .fontWeight(.semibold)
                            Spacer()
                        }
                        .padding(.horizontal)

                        ScrollView(.horizontal, showsIndicators: false) {
                            HStack(spacing: 16) {
                                ForEach(0..<10, id: \.self) { _ in
                                    MediaCard()
                                }
                            }
                            .padding(.horizontal)
                        }
                    }
                }
            }
            .navigationTitle("TV Shows")
        }
    }
}

// MARK: - Search View
struct SearchView: View {
    @State private var searchText = ""

    var body: some View {
        NavigationView {
            VStack {
                if searchText.isEmpty {
                    VStack(spacing: 20) {
                        Image(systemName: "magnifyingglass")
                            .font(.system(size: 50))
                            .foregroundColor(.gray)

                        Text("Search Movies & TV Shows")
                            .font(.title2)
                            .fontWeight(.semibold)

                        Text("Discover your next favorite content")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                } else {
                    List {
                        ForEach(0..<5, id: \.self) { index in
                            HStack {
                                RoundedRectangle(cornerRadius: 8)
                                    .fill(Color.gray.opacity(0.3))
                                    .frame(width: 60, height: 90)

                                VStack(alignment: .leading) {
                                    Text("Search Result \(index + 1)")
                                        .font(.headline)
                                    Text("Movie • 2024")
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                }

                                Spacer()
                            }
                            .padding(.vertical, 4)
                        }
                    }
                }
            }
            .navigationTitle("Search")
            .searchable(text: $searchText, prompt: "Search movies and TV shows")
        }
    }
}

// MARK: - Profile View
struct ProfileView: View {
    var body: some View {
        NavigationView {
            VStack(spacing: 30) {
                // Profile Header
                VStack(spacing: 16) {
                    Circle()
                        .fill(Color.blue.opacity(0.2))
                        .frame(width: 100, height: 100)
                        .overlay(
                            Image(systemName: "person.fill")
                                .font(.system(size: 40))
                                .foregroundColor(.blue)
                        )

                    Text("Movie Enthusiast")
                        .font(.title2)
                        .fontWeight(.semibold)
                }

                // Stats Section
                HStack(spacing: 40) {
                    VStack {
                        Text("42")
                            .font(.title)
                            .fontWeight(.bold)
                        Text("Movies")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }

                    VStack {
                        Text("18")
                            .font(.title)
                            .fontWeight(.bold)
                        Text("TV Shows")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }

                    VStack {
                        Text("8.2")
                            .font(.title)
                            .fontWeight(.bold)
                        Text("Avg Rating")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }

                // Settings List
                VStack(spacing: 0) {
                    NavigationLink(destination: CloudKitSyncView()) {
                        ProfileRow(icon: "icloud", title: "iCloud Sync", showChevron: true)
                    }
                    .buttonStyle(PlainButtonStyle())

                    ProfileRow(icon: "bell", title: "Notifications", showChevron: true)
                    ProfileRow(icon: "moon", title: "Dark Mode", showChevron: true)
                    ProfileRow(icon: "gear", title: "Settings", showChevron: true)
                    ProfileRow(icon: "questionmark.circle", title: "Help & Support", showChevron: true)
                }
                .background(Color.gray.opacity(0.1))
                .cornerRadius(12)
                .padding(.horizontal)

                Spacer()
            }
            .padding(.top, 20)
            .navigationTitle("Profile")
        }
    }
}

struct ProfileRow: View {
    let icon: String
    let title: String
    let showChevron: Bool

    var body: some View {
        HStack {
            Image(systemName: icon)
                .foregroundColor(.blue)
                .frame(width: 24)

            Text(title)
                .font(.body)

            Spacer()

            if showChevron {
                Image(systemName: "chevron.right")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .padding()
        .background(Color.white)
    }
}

// MARK: - Supporting Views
struct MediaCarouselSection<Content: View>: View {
    let title: String
    let subtitle: String
    let content: Content
    
    init(title: String, subtitle: String, @ViewBuilder content: () -> Content) {
        self.title = title
        self.subtitle = subtitle
        self.content = content()
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            VStack(alignment: .leading, spacing: 4) {
                HStack {
                    VStack(alignment: .leading, spacing: 2) {
                        Text(title)
                            .font(.title3)
                            .fontWeight(.semibold)
                        
                        Text(subtitle)
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    Spacer()
                    
                    Button("See All") {
                        // Handle see all action
                    }
                    .font(.caption)
                    .foregroundColor(.blue)
                }
                .padding(.horizontal)
            }
            
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 16) {
                    content
                }
                .padding(.horizontal)
            }
        }
    }
}

struct MediaCard: View {
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.gray.opacity(0.3))
                .frame(width: 120, height: 180)
                .overlay(
                    Image(systemName: "photo")
                        .font(.title)
                        .foregroundColor(.gray)
                )
            
            VStack(alignment: .leading, spacing: 2) {
                Text("Movie Title")
                    .font(.caption)
                    .fontWeight(.medium)
                    .lineLimit(2)
                
                Text("2024")
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
        }
        .frame(width: 120)
    }
}

struct ContinueWatchingCard: View {
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            ZStack(alignment: .bottomLeading) {
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.gray.opacity(0.3))
                    .frame(width: 200, height: 120)
                    .overlay(
                        Image(systemName: "play.circle.fill")
                            .font(.title)
                            .foregroundColor(.white)
                    )
                
                // Progress bar
                VStack {
                    Spacer()
                    ProgressView(value: 0.6)
                        .progressViewStyle(LinearProgressViewStyle(tint: .blue))
                        .scaleEffect(x: 1, y: 0.5)
                        .padding(.horizontal, 8)
                        .padding(.bottom, 8)
                }
            }
            
            VStack(alignment: .leading, spacing: 2) {
                Text("Show Title")
                    .font(.caption)
                    .fontWeight(.medium)
                    .lineLimit(1)
                
                Text("S1 E5 • 23 min left")
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
        }
        .frame(width: 200)
    }
}

#Preview {
    ContentView()
}
