# TVTracker - Beautiful iOS TV & Movie Tracker

A beautiful, feature-rich iOS app for tracking your favorite movies and TV shows, built with SwiftUI and following Apple's Human Interface Guidelines.

## Features

### 🎬 Core Features
- **Comprehensive Media Tracking**: Track movies and TV shows with detailed episode-level progress
- **TMDB Integration**: Access to extensive movie and TV show database with metadata, cast, reviews, and images
- **AI-Powered Recommendations**: Smart recommendations based on your viewing history and ratings using Core ML
- **iCloud Sync**: Seamlessly sync your data across all your Apple devices using CloudKit
- **Beautiful Animations**: Smooth, Apple guideline-compliant animations throughout the app
- **Accessibility First**: Full VoiceOver support and accessibility features

### 🎨 Design & UX
- **Apple Design Guidelines**: Follows latest iOS design patterns and animations
- **Dark Mode Support**: Optimized for both light and dark appearances
- **Dynamic Type**: Supports all accessibility text sizes
- **Haptic Feedback**: Thoughtful haptic responses for better user experience
- **Smooth Transitions**: Custom animations for navigation and state changes

### 🔍 Smart Features
- **Advanced Search**: Search across movies, TV shows, and people with real-time results
- **Continue Watching**: Pick up where you left off with progress tracking
- **Personalized Home**: AI-curated content based on your preferences
- **Rating System**: Rate and review your watched content
- **Watchlist Management**: Add content to your watchlist for later viewing

### 📱 Technical Features
- **SwiftUI**: Modern declarative UI framework
- **Core Data + CloudKit**: Local data persistence with iCloud sync
- **Async/Await**: Modern concurrency for smooth performance
- **MVVM Architecture**: Clean, maintainable code structure
- **Combine Framework**: Reactive programming for data flow

## Requirements

- iOS 17.0+
- Xcode 15.0+
- Swift 5.9+
- TMDB API Key (free registration required)

## Setup Instructions

### 1. Clone the Repository
```bash
git clone https://github.com/yourusername/tvtracker.git
cd tvtracker
```

### 2. Get TMDB API Key
1. Visit [The Movie Database (TMDB)](https://www.themoviedb.org/)
2. Create a free account
3. Go to Settings > API
4. Request an API key (choose "Developer" option)
5. Copy your API key

### 3. Configure API Key
1. Open `TVTracker/Services/TMDBService.swift`
2. Replace `"YOUR_TMDB_API_KEY"` with your actual API key:
```swift
private let apiKey = "your_actual_api_key_here"
```

### 4. Enable iCloud Sync (Optional)
1. In Xcode, select your project
2. Go to Signing & Capabilities
3. Add CloudKit capability
4. Follow the [CloudKit Setup Guide](CloudKit-Setup-Guide.md) for detailed instructions

### 5. Open in Xcode
1. Open `TVTracker.xcodeproj` in Xcode
2. Select your development team in the project settings
3. Choose your target device or simulator
4. Build and run the project (⌘+R)

## Project Structure

```
TVTracker/
├── App/
│   ├── TVTrackerApp.swift          # Main app entry point
│   └── ContentView.swift           # Root view with tab navigation
├── Models/
│   └── DataModel.xcdatamodeld      # Core Data model
├── Services/
│   ├── TMDBService.swift           # TMDB API integration
│   └── RecommendationEngine.swift  # AI recommendation system
├── Views/
│   ├── Components/
│   │   └── AnimatedComponents.swift # Reusable animated UI components
│   ├── DetailView.swift            # Movie/TV show detail view
│   ├── SearchView.swift            # Enhanced search functionality
│   └── CloudKitSyncView.swift      # iCloud sync status and management
└── Resources/
    └── Assets.xcassets             # App icons and images
```

## Key Components

### TMDB Service
- Handles all API communication with The Movie Database
- Provides movie, TV show, and search functionality
- Includes image URL generation for posters and backdrops
- Implements proper error handling and async/await patterns

### Recommendation Engine
- Analyzes user viewing history and ratings
- Generates personalized recommendations using preference scoring
- Combines user preferences with trending content for diversity
- Designed to integrate with Core ML models for advanced recommendations

### Animated Components
- Custom SwiftUI components with Apple-style animations
- Includes media cards, progress indicators, and transition effects
- Accessibility-first design with proper labels and hints
- Optimized for performance with efficient animation patterns

### Core Data + CloudKit Integration
- Stores user data locally for offline access
- Syncs seamlessly across devices using CloudKit
- Tracks watch history, ratings, and progress
- Includes proper relationship management between entities
- Automatic conflict resolution and data merging

## Customization

### Adding New Features
1. **New Views**: Add to `Views/` directory following MVVM pattern
2. **API Endpoints**: Extend `TMDBService.swift` with new methods
3. **Data Models**: Update Core Data model and regenerate classes
4. **Animations**: Add to `AnimatedComponents.swift` for reusability

### Styling
- Colors and themes are defined in `Assets.xcassets`
- Custom animations follow Apple's spring and easing curves
- Typography uses system fonts with Dynamic Type support

## API Usage

The app uses TMDB API v3 with the following endpoints:
- `/trending/movie/week` - Trending movies
- `/trending/tv/week` - Trending TV shows
- `/movie/popular` - Popular movies
- `/tv/popular` - Popular TV shows
- `/search/multi` - Multi-search functionality
- `/movie/{id}` - Movie details
- `/tv/{id}` - TV show details

## Privacy & Data

- All user data is stored locally using Core Data
- iCloud sync uses the user's private CloudKit database
- No personal information is sent to external services
- TMDB API calls only include search queries and content IDs
- App follows Apple's privacy guidelines and data minimization principles
- Users control their own sync settings and can disable iCloud sync anytime

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Acknowledgments

- [The Movie Database (TMDB)](https://www.themoviedb.org/) for providing the comprehensive movie and TV database
- Apple for the excellent SwiftUI framework and design guidelines
- The iOS development community for inspiration and best practices

## Support

If you encounter any issues or have questions:
1. Check the [Issues](https://github.com/yourusername/tvtracker/issues) page
2. Create a new issue with detailed information
3. Include your iOS version, Xcode version, and steps to reproduce

---

**Note**: This app is for educational and personal use. Please respect TMDB's terms of service and rate limiting guidelines when using their API.
